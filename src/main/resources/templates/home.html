<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:lang="${#locale.language}"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout}">
<head>
    <title th:text="#{home.welcome}">Home</title>
    <style>
        .table .task-not-started,
        .table .task-not-started > td {
            --bs-table-bg: #f8f9fa !important;
            background-color: #f8f9fa !important; /* Light gray background */
        }
        .table .task-started,
        .table .task-started > td {
            --bs-table-bg: #d4edda !important;
            background-color: #d4edda !important; /* Light green background */
        }

        /* Additional specificity to override Bootstrap table striping */
        .table-striped .task-not-started,
        .table-striped .task-not-started > td {
            --bs-table-accent-bg: #f8f9fa !important;
            background-color: #f8f9fa !important;
        }
        .table-striped .task-started,
        .table-striped .task-started > td {
            --bs-table-accent-bg: #d4edda !important;
            background-color: #d4edda !important;
        }
    </style>
</head>

<section layout:fragment="content">
    <h2 th:text="#{home.welcome}">Welcome!</h2>

    <div>
        <div th:if="${tasks.isEmpty()}"><h3 th:text="#{home.no.tasks}">There are currently no tasks</h3></div>
        <table th:if="${!tasks.isEmpty()}" class="table table-striped">
            <thead>
            <tr>
                <th th:text="#{home.tasks.title}">Title</th>
                <th th:text="#{home.tasks.description}">Description</th>
                <th th:text="#{home.tasks.planned.start}">Planned start</th>
                <th th:text="#{home.tasks.expected.finish}">Expected finish</th>
                <th th:text="#{home.tasks.finished.at}">Finished At</th>
                <th th:text="#{label.actions}">Actions</th>
            </tr>
            </thead>
            <tbody>
            <tr th:each="task : ${tasks}"
                th:class="${task.startedAt == null ? 'task-not-started' : 'task-started'}">
                <td th:text="${task.title}"></td>
                <td th:text="${task.description}"></td>
                <td th:text="${task.plannedStart != null ? #temporals.format(task.plannedStart, 'yyyy-MM-dd HH:mm') : ''}"></td>
                <td th:text="${task.getExpectedEnd() != null ? #temporals.format(task.getExpectedEnd(), 'yyyy-MM-dd HH:mm') : ''}"></td>
                <td th:text="${task.realizationDate != null ? #temporals.format(task.realizationDate, 'yyyy-MM-dd HH:mm') : ''}"></td>
                <td>
                    <form th:if="${task.startedAt == null}"
                          th:action="@{/tasks/{id}/start(id=${task.id})}"
                          method="post"
                          style="display:inline">
                        <button type="submit" class="btn btn-sm btn-primary" th:text="#{action.start}">Start</button>
                    </form>
                    <form th:if="${task.startedAt != null and task.realizationDate == null}"
                          th:action="@{/tasks/{id}/finish(id=${task.id})}"
                          method="post"
                          style="display:inline">
                        <button type="submit" class="btn btn-sm btn-danger" th:text="#{action.finish}">Finish</button>
                    </form>
                </td>
            </tr>
            </tbody>
        </table>
        <a th:href="@{/tasks/new}" class="btn btn-sm btn-primary" th:text="#{action.new}">New</a>
        <a th:href="@{/tasks}" class="btn btn-sm btn-primary" th:text="#{action.show.all}">Show All</a>
    </div>

    <div class="row mb-4" style="margin-top: 2rem;">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0" th:text="#{home.work.hours.chart.title}">Odpracované hodiny podľa používateľov</h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="prevWeekBtn">
                            <i class="fas fa-chevron-left"></i> Predchádzajúci týždeň
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="currentWeekBtn">
                            Aktuálny týždeň
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="nextWeekBtn">
                            Nasledujúci týždeň <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="weekTitle" class="text-center mb-3">
                        <h6 class="text-muted">Načítavam...</h6>
                    </div>

                    <!-- User Toggle Controls -->
                    <div id="userControls" class="mb-3" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted">Zobraziť/skryť používateľov:</small>
                            <div>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="showAllUsersBtn">
                                    Zobraziť všetkých
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="hideAllUsersBtn">
                                    Skryť všetkých
                                </button>
                            </div>
                        </div>
                        <div id="userToggleButtons" class="d-flex flex-wrap gap-1">
                            <!-- User toggle buttons will be dynamically added here -->
                        </div>
                    </div>

                    <div id="chartContainer">
                        <div id="chartLoading" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Načítavam údaje o odpracovaných hodinách...</p>
                        </div>
                        <canvas id="workHoursChart" width="400" height="200" style="display: none;"></canvas>
                        <div id="chartError" class="text-center text-muted" style="display: none;">
                            <p>Zatiaľ nie sú k dispozícii žiadne údaje o odpracovaných hodinách pre tento týždeň.</p>
                            <small>Údaje sa zobrazia po dokončení úloh s vyplnenými skutočnými hodinami.</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<th:block layout:fragment="scripts">
    <script>
        let currentWeekStart = null;
        let currentChart = null;

        // Function to fetch weekly chart data and render the chart
        async function loadWeeklyWorkHoursChart(weekStart = null) {
            const loadingElement = document.getElementById('chartLoading');
            const chartCanvas = document.getElementById('workHoursChart');
            const errorElement = document.getElementById('chartError');
            const weekTitleElement = document.getElementById('weekTitle');

            // Show loading
            loadingElement.style.display = 'block';
            chartCanvas.style.display = 'none';
            errorElement.style.display = 'none';

            try {
                console.log('Fetching weekly work hours data for week:', weekStart);

                // Build URL with optional week parameter
                let url = '/api/charts/weekly-work-hours';
                if (weekStart) {
                    url += `?weekStart=${weekStart}`;
                }

                // Fetch data from the API endpoint
                const response = await fetch(url);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Received weekly data:', data);

                // Update current week start for navigation
                currentWeekStart = data.weekStart;

                // Update week title
                weekTitleElement.innerHTML = `<h6 class="text-muted">${data.weekTitle}</h6>`;

                // Hide loading spinner
                loadingElement.style.display = 'none';

                // Check if we have data
                if (!data.success || !data.datasets || data.datasets.length === 0) {
                    console.warn('No chart data available for this week');
                    errorElement.style.display = 'block';
                    return;
                }

                // Show chart canvas and create the chart
                chartCanvas.style.display = 'block';
                createChart(data.datasets, data.weekTitle, data.canViewAllUsers, data.userNames);

            } catch (error) {
                console.error('Error fetching weekly chart data:', error);
                loadingElement.style.display = 'none';
                errorElement.style.display = 'block';
                errorElement.innerHTML = '<p class="text-danger">Chyba pri načítavaní údajov o odpracovaných hodinách.</p>';
            }
        }

        // Function to create the chart
        function createChart(datasets, weekTitle, canViewAllUsers, userNames) {
            console.log('Creating weekly chart with', datasets.length, 'users for', weekTitle);

            // Destroy existing chart if it exists
            if (currentChart) {
                currentChart.destroy();
            }

            // Show/hide user controls based on permissions and data
            const userControlsElement = document.getElementById('userControls');
            if (canViewAllUsers && datasets.length > 1) {
                userControlsElement.style.display = 'block';
                createUserToggleButtons(datasets);
            } else {
                userControlsElement.style.display = 'none';
            }

            // Create the chart using bar type with user visibility control
            const ctx = document.getElementById('workHoursChart').getContext('2d');
            currentChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: datasets.map(dataset => dataset.label), // User names as labels
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Hodiny'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Používatelia'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            onClick: function(e, legendItem, legend) {
                                // Toggle dataset visibility
                                const index = legendItem.datasetIndex;
                                const chart = legend.chart;
                                const meta = chart.getDatasetMeta(index);

                                meta.hidden = meta.hidden === null ? !chart.data.datasets[index].hidden : null;
                                chart.update();

                                // Update toggle button state
                                updateToggleButtonState(index, meta.hidden);
                            }
                        },
                        title: {
                            display: true,
                            text: weekTitle
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + ' hodín';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Function to create user toggle buttons
        function createUserToggleButtons(datasets) {
            const container = document.getElementById('userToggleButtons');
            container.innerHTML = ''; // Clear existing buttons

            datasets.forEach((dataset, index) => {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn btn-sm btn-outline-primary user-toggle-btn';
                button.dataset.index = index;
                button.innerHTML = `
                    <span class="color-indicator" style="background-color: ${dataset.backgroundColor}; width: 12px; height: 12px; display: inline-block; border-radius: 50%; margin-right: 5px;"></span>
                    ${dataset.label}
                `;

                button.addEventListener('click', function() {
                    toggleUser(index);
                });

                container.appendChild(button);
            });
        }

        // Function to toggle user visibility
        function toggleUser(index) {
            if (currentChart) {
                const meta = currentChart.getDatasetMeta(index);
                meta.hidden = meta.hidden === null ? !currentChart.data.datasets[index].hidden : null;
                currentChart.update();
                updateToggleButtonState(index, meta.hidden);
            }
        }

        // Function to update toggle button state
        function updateToggleButtonState(index, isHidden) {
            const button = document.querySelector(`[data-index="${index}"]`);
            if (button) {
                if (isHidden) {
                    button.classList.remove('btn-outline-primary');
                    button.classList.add('btn-outline-secondary');
                    button.style.opacity = '0.5';
                } else {
                    button.classList.remove('btn-outline-secondary');
                    button.classList.add('btn-outline-primary');
                    button.style.opacity = '1';
                }
            }
        }

        // Function to show all users
        function showAllUsers() {
            if (currentChart) {
                currentChart.data.datasets.forEach((dataset, index) => {
                    const meta = currentChart.getDatasetMeta(index);
                    meta.hidden = null;
                    updateToggleButtonState(index, false);
                });
                currentChart.update();
            }
        }

        // Function to hide all users
        function hideAllUsers() {
            if (currentChart) {
                currentChart.data.datasets.forEach((dataset, index) => {
                    const meta = currentChart.getDatasetMeta(index);
                    meta.hidden = true;
                    updateToggleButtonState(index, true);
                });
                currentChart.update();
            }
        }

        // Navigation functions
        function goToPreviousWeek() {
            if (currentWeekStart) {
                const prevWeek = new Date(currentWeekStart);
                prevWeek.setDate(prevWeek.getDate() - 7);
                const prevWeekStr = prevWeek.toISOString().split('T')[0];
                loadWeeklyWorkHoursChart(prevWeekStr);
            }
        }

        function goToNextWeek() {
            if (currentWeekStart) {
                const nextWeek = new Date(currentWeekStart);
                nextWeek.setDate(nextWeek.getDate() + 7);
                const nextWeekStr = nextWeek.toISOString().split('T')[0];
                loadWeeklyWorkHoursChart(nextWeekStr);
            }
        }

        function goToCurrentWeek() {
            loadWeeklyWorkHoursChart(); // No parameter = current week
        }

        // Initialize the chart and navigation when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Load current week data
            loadWeeklyWorkHoursChart();

            // Add event listeners for navigation buttons
            document.getElementById('prevWeekBtn').addEventListener('click', goToPreviousWeek);
            document.getElementById('nextWeekBtn').addEventListener('click', goToNextWeek);
            document.getElementById('currentWeekBtn').addEventListener('click', goToCurrentWeek);

            // Add event listeners for user control buttons
            document.getElementById('showAllUsersBtn').addEventListener('click', showAllUsers);
            document.getElementById('hideAllUsersBtn').addEventListener('click', hideAllUsers);
        });
    </script>
</th:block>

</html>
